import { useRef, useState } from "react"
import { UsageGuidelines } from "@/components"
import { Button, Portal, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

/**
 * Portal component
 *
 * The Portal component is a utility component that allows you to render its children into a different
 * part of the DOM tree. This is useful for scenarios like modals, tooltips, and popovers, where you
 * want to render content outside of the normal document flow.
 *
 * Notes:
 * - Built around React's createPortal() API for maximum compatibility
 * - Supports theme scoping to maintain design system consistency
 * - Can render to document.body by default or a custom container
 * - Supports base component integration for advanced use cases
 * - Maintains proper theme context when portaling content
 */
const meta: Meta<typeof Portal> = {
  title: "@apollo∕ui/Components/Utilities/Portal",
  component: Portal,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    controls: { expanded: true },
    docs: {
      description: {
        component:
          "The Portal component is a utility that renders its children into a different part of the DOM tree. It's essential for creating overlays, modals, tooltips, and other components that need to break out of their normal document flow while maintaining theme consistency.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Portal } from "@apollo/ui"`} language="tsx" />
          <h2 id="portal-props">Props</h2>
          <ArgTypes />
          <h3>Best Practices</h3>
          <UsageGuidelines
            guidelines={[
              "Use Portal for components that need to render outside their parent container (modals, tooltips, dropdowns)",
              "Portal automatically maintains theme context when rendering content",
              "Specify a custom container when you need precise control over where content is rendered",
              "Portal is commonly used internally by other components like Modal, Select, and Toast",
              "Content rendered through Portal will inherit the theme from its original location",
            ]}
          />
          <h2 id="portal-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  argTypes: {
    children: {
      control: false,
      description: "The content to be rendered within the Portal.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    container: {
      control: false,
      description:
        "The DOM element into which the children will be rendered. Defaults to document.body.",
      table: {
        type: { summary: "HTMLElement | null" },
        defaultValue: { summary: "document.body" },
      },
    },
  },
  args: {},
}

export default meta
type Story = StoryObj<typeof Portal>

/** Basic Portal usage demonstrating content rendered outside normal flow */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic Portal usage showing how content can be rendered outside its normal parent container. The red-styled content will not inherit the red color when rendered through the Portal.",
      },
    },
  },
  render: (args) => {
    const [show, setShow] = useState(false)
    const containerRef = useRef<HTMLDivElement>(null)

    return (
      <div>
        <Button onClick={() => setShow(!show)}>
          {show ? "Hide Portal Content" : "Show Portal Content"}
        </Button>

        <div
          style={{
            padding: "16px",
            margin: "16px 0",
            border: "2px solid var(--apl-alias-color-error-error)",
            borderRadius: "8px",
            color: "var(--apl-alias-color-error-error)",
            backgroundColor: "var(--apl-alias-color-error-error-container)",
          }}
        >
          <Typography level="bodyMedium">
            This container has negative styling applied.
          </Typography>
          {show && (
            <Portal container={containerRef.current} {...args}>
              <div
                style={{
                  padding: "12px",
                  margin: "8px",
                  border: "2px solid var(--apl-alias-color-primary-primary)",
                  borderRadius: "8px",
                  backgroundColor: "#f0f9f4",
                  color: "var(--apl-alias-color-primary-primary)",
                }}
              >
                <Typography level="bodyMedium">
                  This content is rendered through Portal and doesn't inherit
                  the negative styling!
                </Typography>
              </div>
            </Portal>
          )}
        </div>

        <div
          ref={containerRef}
          style={{
            padding: "16px",
            border: "2px dashed var(--apl-alias-color-secondary-secondary)",
            borderRadius: "8px",
            backgroundColor: "#f8f9fa",
            minHeight: "60px",
          }}
        >
          <Typography
            level="bodySmall"
            style={{ color: "var(--apl-alias-color-secondary-secondary)" }}
          >
            Portal target container (content will appear here when shown)
          </Typography>
        </div>
      </div>
    )
  },
}

/** Practical tooltip-like example */
export const TooltipExample: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A practical example showing how Portal can be used to create tooltip-like overlays that render outside their parent container to avoid clipping issues.",
      },
    },
  },
  render: (args) => {
    const TooltipExampleComponent = () => {
      const [showTooltip, setShowTooltip] = useState(false)
      const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
      const buttonRef = useRef<HTMLButtonElement>(null)

      const handleMouseEnter = () => {
        if (buttonRef.current) {
          const rect = buttonRef.current.getBoundingClientRect()
          setTooltipPosition({
            x: rect.left + rect.width / 2,
            y: rect.top - 10,
          })
          setShowTooltip(true)
        }
      }

      const handleMouseLeave = () => {
        setShowTooltip(false)
      }

      return (
        <div style={{ padding: "40px", textAlign: "center" }}>
          <Typography level="bodyMedium" style={{ marginBottom: "20px" }}>
            Hover over the button to see a Portal-based tooltip:
          </Typography>

          <div
            style={{
              padding: "20px",
              border: "2px solid var(--apl-alias-color-outline-and-border-outline)",
              borderRadius: "8px",
              backgroundColor: "var(--apl-alias-color-background-and-surface-surface)",
              overflow: "hidden", // This would clip a regular tooltip
              position: "relative",
            }}
          >
            <Typography
              level="bodySmall"
              style={{ marginBottom: "16px", color: "var(--apl-alias-color-secondary-secondary)" }}
            >
              Container with overflow: hidden (would clip regular tooltips)
            </Typography>

            <Button
              ref={buttonRef}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              {...args}
            >
              Hover for Tooltip
            </Button>
          </div>

          {showTooltip && (
            <Portal {...args}>
              <div
                style={{
                  position: "fixed",
                  left: tooltipPosition.x,
                  top: tooltipPosition.y,
                  transform: "translate(-50%, -100%)",
                  padding: "8px 12px",
                  backgroundColor: "var(--apl-alias-color-tertiary-tertiary)",
                  color: "white",
                  borderRadius: "4px",
                  fontSize: "14px",
                  zIndex: 1000,
                  pointerEvents: "none",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                }}
              >
                This tooltip renders via Portal!
                <div
                  style={{
                    position: "absolute",
                    top: "100%",
                    left: "50%",
                    transform: "translateX(-50%)",
                    width: 0,
                    height: 0,
                    borderLeft: "5px solid transparent",
                    borderRight: "5px solid transparent",
                    borderTop: "5px solid var(--apl-alias-color-tertiary-tertiary)",
                  }}
                />
              </div>
            </Portal>
          )}
        </div>
      )
    }

    return <TooltipExampleComponent />
  },
}
