import { useCallback, useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { CapsuleTab, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"
import { FileImage, FileText, VideoCamera } from "@design-systems/apollo-icons"

/**
 * CapsuleTab component
 *
 * The CapsuleTab component provides a styled, accessible tabbed interface with a capsule-style design.
 * It allows users to switch between different views or content sections with a clean, modern appearance.
 *
 * Notes:
 * - Controlled component requiring selectedIndex and onSelect props;
 * - Supports custom labels including ReactNode for flexible content;
 * - Built with accessibility in mind with proper button semantics.
 */

// Example data
const tabs = [
  { id: "alpha", label: "Alpha" },
  { id: "beta", label: "Beta" },
  { id: "gamma", label: "Gamma" },
]

const meta = {
  title: "@apollo∕ui/Components/Layout/CapsuleTab",
  component: CapsuleTab,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2663-4115&m=dev",
    },
    docs: {
      description: {
        component:
          "The CapsuleTab component renders a tabbed interface with Apollo design system styling. Supports multiple tabs with customizable labels and controlled selection state.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { CapsuleTab } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="capsuletab-props">Props</h2>
          <ArgTypes />
          <h2 id="capsuletab-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use CapsuleTab for navigation between related content sections",
              "Keep tab labels concise and descriptive - ideally 1-2 words",
              "Limit the number of tabs to 2-5 for optimal usability",
              "Ensure tab content is logically grouped and related",
              "Consider the visual hierarchy - tabs should be secondary to main content",
            ]}
          />
          <h2 id="capsuletab-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Provide clear, descriptive <code>label</code> content for each
                tab to ensure the purpose is clear to all users.
              </>,
              "Use semantic button elements for proper keyboard navigation support.",
            ]}
          />
          <h2 id="capsuletab-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The CapsuleTab component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloCapsuleTab-root",
                description: "Styles applied to the capsule tab root container",
                usageNotes: "Use for overall tab styling and positioning",
              },
              {
                cssClassName: ".ApolloCapsuleTab-container",
                description: "Styles applied to the tab container",
                usageNotes:
                  "Contains flex layout and border styling for the tab group",
              },
              {
                cssClassName: ".ApolloCapsuleTab-item",
                description: "Styles applied to individual tab items",
                usageNotes:
                  "Contains tab button styling including padding and typography",
              },
              {
                cssClassName: ".ApolloCapsuleTab-itemSelected",
                description: "Styles applied to the selected tab item",
                usageNotes:
                  "Applies active styling with background color and text color changes",
              },
              {
                cssClassName: ".ApolloCapsuleTab-itemText",
                description: "Styles applied to the text inside tab items",
                usageNotes:
                  "Contains font size and weight styling for tab text",
              },
            ]}
          />
          <h2 id="capsuletab-examples">Examples</h2>
          <Stories title="" />
          <h2 id="capsuletab-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                      }}
                    >
                      <CapsuleTab
                        tabs={[
                          { id: "overview", label: "Overview" },
                          { id: "details", label: "Details" },
                          { id: "settings", label: "Settings" },
                        ]}
                        selectedIndex={0}
                        onSelect={() => {}}
                      />
                    </div>
                  ),
                  description:
                    "Use clear, descriptive labels that indicate the content of each tab section",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                      }}
                    >
                      <CapsuleTab
                        tabs={[
                          { id: "tab1", label: "Tab 1" },
                          { id: "tab2", label: "Tab 2" },
                          { id: "tab3", label: "Tab 3" },
                        ]}
                        selectedIndex={0}
                        onSelect={() => {}}
                      />
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't provide context about the tab content",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                      }}
                    >
                      <CapsuleTab
                        tabs={[
                          { id: "personal", label: "Personal" },
                          { id: "billing", label: "Billing" },
                          { id: "security", label: "Security" },
                        ]}
                        selectedIndex={0}
                        onSelect={() => {}}
                      />
                    </div>
                  ),
                  description:
                    "Keep the number of tabs manageable (3-5) for better usability",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                      }}
                    >
                      <CapsuleTab
                        tabs={[
                          { id: "tab1", label: "One" },
                          { id: "tab2", label: "Two" },
                          { id: "tab3", label: "Three" },
                          { id: "tab4", label: "Four" },
                          { id: "tab5", label: "Five" },
                          { id: "tab6", label: "Six" },
                          { id: "tab7", label: "Seven" },
                        ]}
                        selectedIndex={0}
                        onSelect={() => {}}
                      />
                    </div>
                  ),
                  description:
                    "Don't use too many tabs as it becomes difficult to navigate and read",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    tabs: {
      control: { type: "object" },
      description:
        "Array of tab objects with `id` and `label`. Each tab must have a unique id and a label (string or ReactNode).",
      table: { type: { summary: "CapsuleTabItem[]" } },
    },
    selectedIndex: {
      control: { type: "number" },
      description: "Index of the currently selected tab (0-based).",
      table: { type: { summary: "number" } },
    },
    onSelect: {
      control: false,
      description: "Callback fired when a tab is selected.",
      table: {
        type: {
          summary: "(index: number) => void",
        },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: { type: { summary: "string" } },
    },
    ref: {
      control: false,
      description: "Ref for the underlying div element.",
      table: { type: { summary: "Ref<HTMLDivElement>" } },
    },
  },
  args: {
    tabs,
    selectedIndex: 0,
  },
} satisfies Meta<typeof CapsuleTab>

export default meta

type Story = StoryObj<typeof CapsuleTab>

/** Default CapsuleTab (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview CapsuleTab with default settings. The component requires tabs array and controlled selectedIndex state.",
      },
    },
  },
  render: (args) => {
    function OverviewDemo() {
      const [selectedIndex, setSelectedIndex] = useState(0)

      return (
        <CapsuleTab
          {...args}
          tabs={args.tabs ?? tabs}
          selectedIndex={selectedIndex}
          onSelect={setSelectedIndex}
        />
      )
    }
    return <OverviewDemo />
  },
  args: {
    tabs,
    selectedIndex: 0,
  },
}

/** CapsuleTab with different tab counts */
export const TabVariations: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CapsuleTab with different numbers of tabs to show how the component adapts to various content scenarios.",
      },
    },
  },
  render: () => {
    function TabVariationsDemo() {
      const [selectedTwo, setSelectedTwo] = useState(0)
      const [selectedFour, setSelectedFour] = useState(0)
      const [selectedFive, setSelectedFive] = useState(0)

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 24,
            alignItems: "center",
          }}
        >
          <div style={{ width: "100%" }}>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Two Tabs
            </Typography>
            <CapsuleTab
              tabs={[
                { id: "option1", label: "Option 1" },
                { id: "option2", label: "Option 2" },
              ]}
              selectedIndex={selectedTwo}
              onSelect={setSelectedTwo}
            />
          </div>

          <div style={{ width: "100%" }}>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Four Tabs
            </Typography>
            <CapsuleTab
              tabs={[
                { id: "home", label: "Home" },
                { id: "about", label: "About" },
                { id: "services", label: "Services" },
                { id: "contact", label: "Contact" },
              ]}
              selectedIndex={selectedFour}
              onSelect={setSelectedFour}
            />
          </div>

          <div style={{ width: "100%" }}>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Five Tabs (Maximum Recommended)
            </Typography>
            <CapsuleTab
              tabs={[
                { id: "dashboard", label: "Dashboard" },
                { id: "analytics", label: "Analytics" },
                { id: "reports", label: "Reports" },
                { id: "settings", label: "Settings" },
                { id: "help", label: "Help" },
              ]}
              selectedIndex={selectedFive}
              onSelect={setSelectedFive}
            />
          </div>
        </div>
      )
    }
    return <TabVariationsDemo />
  },
}

/** Controlled CapsuleTab example */
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A controlled CapsuleTab that manages its own state with React hooks and shows current selection.",
      },
    },
  },
  render: () => {
    function ControlledDemo() {
      const [selectedIndex, setSelectedIndex] = useState(0)

      const handleTabChange = useCallback((index: number) => {
        setSelectedIndex(index)
      }, [])

      const tabData = [
        { id: "profile", label: "Profile" },
        { id: "preferences", label: "Preferences" },
        { id: "notifications", label: "Notifications" },
      ]

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 16,
            alignItems: "center",
          }}
        >
          <CapsuleTab
            tabs={tabData}
            selectedIndex={selectedIndex}
            onSelect={handleTabChange}
          />
          <Typography level="bodySmall" style={{ color: "#6b7280" }}>
            Current selection: {tabData[selectedIndex]?.label} (Index:{" "}
            {selectedIndex})
          </Typography>
        </div>
      )
    }
    return <ControlledDemo />
  },
}

/** CapsuleTab with custom content */
export const CustomContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CapsuleTab with ReactNode labels to demonstrate flexible content support including icons or custom elements.",
      },
    },
  },
  render: () => {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
        <CapsuleTab
          tabs={[
            {
              id: "text",
              label: (
                <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
                  <FileText size={16}/> Text
                </span>
              ),
            },
            {
              id: "image",
              label: (
                <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
                  <FileImage size={16}/> Image
                </span>
              ),
            },
            {
              id: "video",
              label: (
                <span style={{ display: "flex", alignItems: "center", gap: 4 }}>
                  <VideoCamera size={16}/> Video
                </span>
              ),
            },
          ]}
          selectedIndex={selectedIndex}
          onSelect={setSelectedIndex}
        />
        <Typography
          level="bodyMedium"
          style={{ textAlign: "center", color: "#6b7280" }}
        >
          Custom ReactNode labels with icon
        </Typography>
      </div>
    )
  },
}

/** Real-world usage example */
export const RealWorldExample: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Example of CapsuleTab integrated into a realistic interface with content switching and state management.",
      },
    },
  },
  render: () => {
    const [activeTab, setActiveTab] = useState(0)

    const tabs = [
      { id: "personal", label: "Personal Info" },
      { id: "billing", label: "Billing" },
      { id: "security", label: "Security" },
    ]

    const renderTabContent = () => {
      switch (activeTab) {
        case 0:
          return (
            <div style={{ padding: 20, textAlign: "center" }}>
              <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
                Personal Information
              </Typography>
              <Typography level="bodySmall" style={{ color: "#6b7280" }}>
                Manage your personal details and contact information
              </Typography>
            </div>
          )
        case 1:
          return (
            <div style={{ padding: 20, textAlign: "center" }}>
              <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
                Billing Settings
              </Typography>
              <Typography level="bodySmall" style={{ color: "#6b7280" }}>
                Update payment methods and billing address
              </Typography>
            </div>
          )
        case 2:
          return (
            <div style={{ padding: 20, textAlign: "center" }}>
              <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
                Security Options
              </Typography>
              <Typography level="bodySmall" style={{ color: "#6b7280" }}>
                Configure password and two-factor authentication
              </Typography>
            </div>
          )
        default:
          return null
      }
    }

    return (
      <div
        style={{
          width: "600px",
          margin: "0 auto",
          border: "1px solid #e5e7eb",
          borderRadius: 8,
          overflow: "hidden",
        }}
      >
        <div style={{ padding: "16px 16px 0 16px" }}>
          <CapsuleTab
            tabs={tabs}
            selectedIndex={activeTab}
            onSelect={setActiveTab}
          />
        </div>
        <div style={{ minHeight: 120 }}>{renderTabContent()}</div>
      </div>
    )
  },
}
