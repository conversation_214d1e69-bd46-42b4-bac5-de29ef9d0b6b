import { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { DateInput, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import { addDays } from "date-fns"
import { InfoCircle } from "@design-systems/apollo-icons"

/**
 * DateInput component
 *
 * The DateInput component provides a styled, accessible date input with calendar popup.
 * It supports various date formats, locales, eras (AD/BD), date ranges, and different
 * view modes (date, month, year selection).
 *
 * Features:
 * - Buddhist Era (BD) and Christian Era (AD) support
 * - Thai and English locales
 * - Custom date formats
 * - Date range selection
 * - Month and year picker modes
 * - Date restrictions (min/max dates, excluded dates)
 * - Portal rendering support
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/DateInput",
  component: DateInput,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ minHeight: "400px" }}>
        <Story />
      </div>
    ),
  ],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2242-5598&m=dev",
    },
    docs: {
      description: {
        component:
          "The DateInput component renders a date input with calendar popup using Apollo design system styling. It supports Buddhist and Christian eras, multiple locales, and various selection modes.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { DateInput } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="dateinput-props">Props</h2>
          <ArgTypes />
          <h2 id="dateinput-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Always provide a clear label that describes what date the user should select",
              "Use placeholder text to provide format hints or examples",
              "Helper text should be used to provide additional context or instructions",
              "Consider the appropriate era (BD/AD) based on your user base and locale",
              "Use date restrictions (minDate, maxDate, excludeDates) when appropriate",
              "For date ranges, clearly indicate the start and end date selection",
              "Choose the appropriate view mode (date, month, year) based on the use case",
              "When using time selection, consider appropriate time intervals for your use case",
              "Use time-only picker for scenarios where date is not relevant",
              "Provide clear format examples when combining date and time",
            ]}
          />
          <h2 id="dateinput-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always use <code>label</code> prop to provide a descriptive
                label for the date picker. The label should clearly indicate
                what date is being selected.
              </>,
              <>
                Use the <code>placeholder</code> prop to provide format hints or
                examples of expected date input.
              </>,
              <>
                Use <code>helperText</code> to provide additional context,
                instructions, or format information.
              </>,
              <>
                Provide clear error messages using <code>error</code> prop with{" "}
                <code>helperText</code> to help users understand validation
                issues.
              </>,
              "For required date fields, use appropriate validation and error messaging to guide users.",
              "Consider keyboard navigation - users should be able to navigate the calendar using arrow keys and select dates with Enter/Space.",
            ]}
          />
          <h2 id="dateinput-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The DateInput component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions."
            data={[
              {
                cssClassName: ".ApolloDatePicker-root",
                description: "Styles applied to the root wrapper element",
                usageNotes:
                  "Use for overall positioning and layout of the date picker",
              },
              {
                cssClassName: ".ApolloDatePicker-calendarHeader",
                description: "Styles applied to the calendar header",
                usageNotes: "Contains month/year navigation and day names",
              },
              {
                cssClassName: ".ApolloDatePicker-prevYearButton",
                description: "Styles applied to the previous year button",
                usageNotes:
                  "Use for styling the previous year navigation button",
              },
              {
                cssClassName: ".ApolloDatePicker-nextYearButton",
                description: "Styles applied to the next year button",
                usageNotes: "Use for styling the next year navigation button",
              },
              {
                cssClassName: ".ApolloDatePicker-prevMonthButton",
                description: "Styles applied to the previous month button",
                usageNotes:
                  "Use for styling the previous month navigation button",
              },
              {
                cssClassName: ".ApolloDatePicker-nextMonthButton",
                description: "Styles applied to the next month button",
                usageNotes: "Use for styling the next month navigation button",
              },
              {
                cssClassName: ".ApolloDatePicker-monthButton",
                description: "Styles applied to the month picker button",
                usageNotes: "Use for styling the month picker button",
              },
              {
                cssClassName: ".ApolloDatePicker-yearButton",
                description: "Styles applied to the year picker button",
                usageNotes: "Use for styling the year picker button",
              },
              {
                cssClassName: ".ApolloDatePicker-day",
                description: "Styles applied to individual day cells",
                usageNotes:
                  "Use for styling individual date cells in the calendar",
              },
              {
                cssClassName: ".ApolloDatePicker-month",
                description: "Styles applied to individual month cells",
                usageNotes:
                  "Use for styling individual month cells in the calendar",
              },
              {
                cssClassName: ".ApolloDatePicker-year",
                description: "Styles applied to individual year cells",
                usageNotes:
                  "Use for styling individual year cells in the calendar",
              },
            ]}
          />
          <h2 id="dateinput-examples">Examples</h2>
          <Stories title="" />
          <h2 id="dateinput-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <DateInput
                        label="Birth Date"
                        placeholder="Select your birth date"
                        helperText="Used for age verification"
                      />
                    </div>
                  ),
                  description:
                    "Use clear, descriptive labels and helpful context",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <DateInput label="Date" placeholder="dd/mm/yyyy" />
                    </div>
                  ),
                  description:
                    "Avoid generic labels and format-only placeholders without context",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <DateInput
                        label="Appointment Date"
                        placeholder="Choose available date"
                        helperText="Available dates are highlighted in the calendar"
                        minDate={new Date()}
                      />
                    </div>
                  ),
                  description:
                    "Use date restrictions and helper text to guide user selection",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <DateInput
                        label="Appointment Date"
                        placeholder="Select date"
                      />
                    </div>
                  ),
                  description:
                    "Don't allow selection of invalid dates without restrictions or guidance",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description: "Label text for the date picker",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    labelDecorator: {
      control: false,
      description: "Element displayed next to the label (ReactNode).",
      table: { type: { summary: "ReactNode" } },
    },
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text shown in the input",
      table: {
        type: { summary: "string" },
      },
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the input",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    helperTextDecorator: {
      control: false,
      description: "Element displayed next to helper text (ReactNode).",
      table: { type: { summary: "ReactNode" } },
    },
    required: {
      control: { type: "boolean" },
      description: "Whether the date picker is required",
      table: {
        type: { summary: "boolean" },
      },
    },
    error: {
      control: { type: "boolean" },
      description: "Whether the date picker is in error state",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the date picker is disabled",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    era: {
      control: { type: "select" },
      options: ["ad", "bd"],
      description: "Calendar era - Buddhist (BD) or Christian (AD)",
      table: {
        defaultValue: { summary: "bd" },
        type: { summary: "bd | ad" },
      },
    },
    locale: {
      control: { type: "select" },
      options: ["th", "en"],
      description: "Locale for date formatting and calendar",
      table: {
        defaultValue: { summary: "th" },
        type: { summary: "th | en" },
      },
    },
    format: {
      control: { type: "text" },
      description: "Custom date format string",
    },
    showMonthYearPicker: {
      control: { type: "boolean" },
      description: "Show month and year picker instead of date picker",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    showYearPicker: {
      control: { type: "boolean" },
      description: "Show year picker only",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    inputProps: {
      control: false,
      description: "Additional props for the input field",
      table: { type: { summary: `"label" | "labelDecorator" | "required" | "helperText" | "helperTextDecorator" | "error" | "placeholder" | "size" | "fullWidth"` } },
    },
    portal: {
      control: { type: "boolean" },
      description: "Render calendar in a portal",
    },
    value: {
      control: false,
      description: "Selected date(s)",
      table: { type: { summary: "Date | null" } },
    },
    onChange: {
      control: false,
      description: "Callback fired when date changes",
      table: {
        type: { summary: "(date: Date | null) => void" },
      },
    },
    startDate: {
      control: false,
      description: "Start date for range selection",
      table: { type: { summary: "Date | null" } },
    },
    endDate: {
      control: false,
      description: "End date for range selection",
      table: { type: { summary: "Date | null" } },
    },
    onViewModeChange: {
      control: false,
      description: "Callback fired when view mode changes",
      table: { type: { summary: "(mode: DatePickerViewMode) => void" } },
    },
    hideCalendarMonth: {
      control: { type: "boolean" },
      description: "Hide month dropdown in calendar header",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    hideCalendarYear: {
      control: { type: "boolean" },
      description: "Hide year dropdown in calendar header",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    shouldBackToDateViewAfterSelect: {
      control: { type: "boolean" },
      description: "Return to date view after selecting month/year",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "true" },
      },
    },
    onBlur: {
      control: false,
      description: "Callback fired when input loses focus",
      table: { type: { summary: "() => void" } },
    },
    showTimeSelect: {
      control: { type: "boolean" },
      description: "Enable time selection alongside date",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    showTimeSelectOnly: {
      control: { type: "boolean" },
      description: "Show only time selection (no date)",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    timeIntervals: {
      control: { type: "number", min: 1, max: 60, step: 1 },
      description: "Time selection intervals in minutes",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "30" },
      },
    },
    timeCaption: {
      control: { type: "text" },
      description: "Caption for the time selection",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "Time" },
      },
    },
  },
  args: {
    label: "Select Date",
    placeholder: "Choose a date",
    error: false,
    disabled: false,
    era: "bd",
    locale: "th",
  },
} satisfies Meta<typeof DateInput>

export default meta

type Story = StoryObj<typeof DateInput>

/** Default DateInput with Buddhist Era */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic DateInput with default Buddhist Era (BD) and Thai locale. Click the calendar icon to open the date picker.",
      },
    },
  },
  render: function OverviewExample() {
    const [date, setDate] = useState<Date | null>(new Date())

    return (
      <DateInput
        label="Birth Date"
        placeholder="Select your birth date"
        value={date}
        onChange={setDate}
        helperText="Buddhist Era calendar (BD)"
      />
    )
  },
  args: {
    label: "Birth Date",
    placeholder: "Select your birth date",
    helperText: "Buddhist Era calendar (BD)",
  },
}

/** DateInput with different eras (Buddhist and Christian) */
export const Eras: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Comparison of Buddhist Era (BD) and Christian Era (AD) date pickers.",
      },
    },
  },
  render: function ErasExample() {
    const [bdDate, setBdDate] = useState<Date | null>(new Date())
    const [adDate, setAdDate] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", gap: 20, alignItems: "flex-start" }}>
        <div>
          <DateInput
            label="Buddhist Era (BD)"
            placeholder="Select date"
            era="bd"
            value={bdDate}
            onChange={setBdDate}
            helperText="Buddhist calendar (+543 years)"
          />
        </div>
        <div>
          <DateInput
            label="Christian Era (AD)"
            placeholder="Select date"
            era="ad"
            value={adDate}
            onChange={setAdDate}
            helperText="Standard Christian calendar"
          />
        </div>
      </div>
    )
  },
}

/** DateInput with different locales */
export const Locales: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "DateInput with Thai and English locales for different language support.",
      },
    },
  },
  render: function LocalesExample() {
    const [thDate, setThDate] = useState<Date | null>(new Date())
    const [enDate, setEnDate] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", gap: 20, alignItems: "flex-start" }}>
        <div>
          <DateInput
            label="Thai Locale"
            placeholder="เลือกวันที่"
            locale="th"
            value={thDate}
            onChange={setThDate}
            helperText="Thai language calendar"
          />
        </div>
        <div>
          <DateInput
            label="English Locale"
            placeholder="Select date"
            locale="en"
            value={enDate}
            onChange={setEnDate}
            helperText="English language calendar"
          />
        </div>
      </div>
    )
  },
}

/** DateInput states showcase */
export const States: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A comprehensive showcase of DateInput states: default, disabled, error, and with helper text.",
      },
    },
  },
  render: function StatesExample() {
    const [normalDate, setNormalDate] = useState<Date | null>(new Date())
    const [decoratorDate, setDecoratorDate] = useState<Date | null>(new Date())
    const [errorDate, setErrorDate] = useState<Date | null>(null)

    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, minmax(250px, 1fr))",
          gap: 20,
          alignItems: "end",
        }}
      >
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Default
          </Typography>
          <DateInput
            label="Event Date"
            placeholder="Select event date"
            value={normalDate}
            onChange={setNormalDate}
            helperText="Choose your preferred date"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Disabled
          </Typography>
          <DateInput
            label="Disabled Date"
            placeholder="Cannot select"
            value={new Date()}
            onChange={() => {}}
            disabled
            helperText="This field is disabled"
          />
        </div>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Label Decorator
          </Typography>
          <DateInput
            label="Label Decorator"
            placeholder="Select date"
            value={decoratorDate}
            onChange={setDecoratorDate}
            helperText="Helper text with decorator"
            labelDecorator={<InfoCircle size={12} />}
            helperTextDecorator={<InfoCircle size={12} />}
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Disabled with placeholder
          </Typography>
          <DateInput
            label="Label Decorator"
            placeholder="Select date"
            helperText="Helper text with decorator"
            disabled
            labelDecorator={<InfoCircle size={12} />}
            helperTextDecorator={<InfoCircle size={12} />}
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Error State
          </Typography>
          <DateInput
            label="Required Date"
            placeholder="Select date"
            value={errorDate}
            onChange={setErrorDate}
            required
            error
            helperText="Please select a valid date"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            With Portal
          </Typography>
          <DateInput
            label="Portal Date"
            placeholder="Select date"
            portal
            helperText="Calendar opens in portal"
          />
        </div>
      </div>
    )
  },
}

/** DateInput with custom formats */
export const CustomFormats: Story = {
  parameters: {
    docs: {
      description: {
        story: "DateInput with different date format options.",
      },
    },
  },
  render: function CustomFormatsExample() {
    const [defaultFormat, setDefaultFormat] = useState<Date | null>(null)
    const [customFormat1, setCustomFormat1] = useState<Date | null>(null)
    const [customFormat2, setCustomFormat2] = useState<Date | null>(null)

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Default Format (dd MMM bbbb)
          </Typography>
          <DateInput
            label="Default Format"
            placeholder="Select date"
            value={defaultFormat}
            onChange={setDefaultFormat}
            helperText="Default Buddhist Era format"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Custom Format (dd/MM/bbbb)
          </Typography>
          <DateInput
            label="Custom Format"
            placeholder="Select date"
            format="dd/MM/bbbb"
            value={customFormat1}
            onChange={setCustomFormat1}
            helperText="Day/Month/Year format"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            AD Format (dd-MM-yyyy)
          </Typography>
          <DateInput
            label="AD Format"
            placeholder="Select date"
            era="ad"
            format="dd-MM-yyyy"
            value={customFormat2}
            onChange={setCustomFormat2}
            helperText="Christian Era with dashes"
          />
        </div>
      </div>
    )
  },
}

/** DateInput with different view modes */
export const ViewModes: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "DateInput with different selection modes: date, month, and year pickers.",
      },
    },
  },
  render: function ViewModesExample() {
    const [dateMode, setDateMode] = useState<Date | null>(new Date())
    const [monthMode, setMonthMode] = useState<Date | null>(new Date())
    const [yearMode, setYearMode] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Date Picker (Default)
          </Typography>
          <DateInput
            label="Select Date"
            placeholder="Choose a specific date"
            value={dateMode}
            onChange={setDateMode}
            helperText="Standard date picker with full calendar"
            format="dd MMM bbbb"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Month Picker
          </Typography>
          <DateInput
            label="Select Month"
            placeholder="Choose month and year"
            showMonthYearPicker
            value={monthMode}
            onChange={setMonthMode}
            helperText="Month and year selection only"
            format="MMMM bbbb"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Year Picker
          </Typography>
          <DateInput
            label="Select Year"
            placeholder="Choose year"
            showYearPicker
            value={yearMode}
            onChange={setYearMode}
            helperText="Year selection only"
            format="yyyy"
          />
        </div>
      </div>
    )
  },
}

/** DateInput with date restrictions */
export const DateRestrictions: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "DateInput with various date restrictions: min/max dates and excluded dates.",
      },
    },
  },
  render: function DateRestrictionsExample() {
    const [futureDate, setFutureDate] = useState<Date | null>(null)
    const [pastDate, setPastDate] = useState<Date | null>(null)
    const [excludedDate, setExcludedDate] = useState<Date | null>(null)

    const today = new Date()
    const excludedDates = [
      addDays(today, 1),
      addDays(today, 2),
      addDays(today, 5),
    ]

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Future Dates Only
          </Typography>
          <DateInput
            label="Appointment Date"
            placeholder="Select future date"
            minDate={today}
            value={futureDate}
            onChange={setFutureDate}
            helperText="Only dates from today onwards are available"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Past Dates Only
          </Typography>
          <DateInput
            label="Birth Date"
            placeholder="Select past date"
            maxDate={today}
            value={pastDate}
            onChange={setPastDate}
            helperText="Only dates up to today are available"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            With Excluded Dates
          </Typography>
          <DateInput
            label="Available Date"
            placeholder="Select available date"
            excludeDates={excludedDates}
            value={excludedDate}
            onChange={setExcludedDate}
            helperText="Some dates are unavailable (grayed out)"
          />
        </div>
      </div>
    )
  },
}

/** DateInput with range selection */
export const DateRange: Story = {
  parameters: {
    docs: {
      description: {
        story: "DateInput with date range selection capability.",
      },
    },
  },
  render: function DateRangeExample() {
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
      null,
      null,
    ])

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Date Range Selection
          </Typography>
          <DateInput
            label="Select Date Range"
            placeholder="Choose start and end dates"
            isRange
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={setDateRange}
            helperText="Click to select start date, then end date"
          />
        </div>

        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "4px",
            fontSize: "14px",
          }}
        >
          <strong>Selected Range:</strong>
          <br />
          Start:{" "}
          {dateRange[0] ? dateRange[0].toLocaleDateString() : "Not selected"}
          <br />
          End:{" "}
          {dateRange[1] ? dateRange[1].toLocaleDateString() : "Not selected"}
        </div>
      </div>
    )
  },
}

/** DateInput with time selection */
export const DateTime: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "DateInput with time selection functionality. Allows users to select both date and time in a single component.",
      },
    },
  },
  render: function DateTimeExample() {
    const [dateTime, setDateTime] = useState<Date | null>(new Date())
    const [dateTimeCustom, setDateTimeCustom] = useState<Date | null>(new Date())
    const [timeOnly, setTimeOnly] = useState<Date | null>(new Date())

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Date and Time Picker
          </Typography>
          <DateInput
            label="Appointment DateTime"
            placeholder="Select date and time"
            showTimeSelect
            value={dateTime}
            onChange={setDateTime}
            helperText="Select both date and time"
            format="dd MMM bbbb HH:mm"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Date and Time with Custom Intervals
          </Typography>
          <DateInput
            label="Meeting DateTime"
            placeholder="Select date and time"
            showTimeSelect
            timeIntervals={15}
            timeCaption="Time"
            value={dateTimeCustom}
            onChange={setDateTimeCustom}
            helperText="15-minute intervals for time selection"
            format="dd MMM bbbb HH:mm"
          />
        </div>

        <div>
          <Typography level="bodyLarge" style={{ marginBottom: 8 }}>
            Time Only Picker
          </Typography>
          <DateInput
            label="Select Time"
            placeholder="Choose time"
            showTimeSelect
            showTimeSelectOnly
            value={timeOnly}
            onChange={setTimeOnly}
            helperText="Time selection only"
            format="HH:mm"
          />
        </div>

        <div
          style={{
            padding: "12px",
            background: "#f8f9fa",
            borderRadius: "4px",
            fontSize: "14px",
          }}
        >
          <strong>Selected Values:</strong>
          <br />
          DateTime: {dateTime ? dateTime.toLocaleString() : "Not selected"}
          <br />
          Custom DateTime: {dateTimeCustom ? dateTimeCustom.toLocaleString() : "Not selected"}
          <br />
          Time Only: {timeOnly ? timeOnly.toLocaleTimeString() : "Not selected"}
        </div>
      </div>
    )
  },
}
