import React, { useEffect, useState } from "react"
import { Button, Typography } from "@apollo/ui"
import {
  Appstore,
  Heart,
  History,
  Shop,
  Star,
} from "@design-systems/apollo-icons"
import ResourceCard from "../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="Introduction" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >Apollo Design System</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Resources for building your UI with great experiences!</Typography>
  <div
    style={{
      display: "flex",
      gap: 8,
      flexWrap: "wrap",
      justifyContent: "center",
      padding: "32px",
      minWidth: "460px",
    }}
  >
    <img src="/logo.svg" alt="Apollo UI Logo" style={{ maxWidth: "100%" }} />
  </div>
</div>

<Typography level="headlineLarge" style={{ margin: "0px", color: "#121212" }}>Our Resources</Typography>


<Typography
  level="bodyLarge"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 32,
  }}
>
  Apollo Design System is a set of UI components and utilities resulting from an effort to converge the set of React <a href="https://base-ui.com/react/overview/quick-start" target="_blank" style={{ color: "var(--sb-link-color)" }}>base-ui</a> component libraries in production today: <code style={{ background: "rgb(247, 250, 252)", color: "rgba(46, 52, 56, 0.9)", padding: "3px 5px", borderRadius: 3 }}>@apollo∕ui</code> as main package along with <code style={{ background: "rgb(247, 250, 252)", color: "rgba(46, 52, 56, 0.9)", padding: "3px 5px", borderRadius: 3 }}>@apollo∕storefront</code> for mobile development.
</Typography>

{/* Card-based resources */}

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
  }}
>
  <ResourceCard
    page="apollo∕ui"
    title="@apollo/ui"
    description="Main UI components (Inputs, Data Display, Feedback, etc.)"
    icon={<Appstore size={24} />}
  />
  <ResourceCard
    page="apollo∕storefront"
    title="@apollo/storefront"
    description="Storefront components for mobile development"
    icon={<Shop size={24} />}
  />
  <ResourceCard
    page="Foundations"
    title="Foundations"
    description="Tokens and design primitives"
    icon={<Heart size={24} />}
  />
  <ResourceCard
    page="icons"
    title="Icons"
    description="SVG icon set"
    icon={<Star size={24} />}
  />
  <ResourceCard
    page="design-systems∕apollo-ui"
    title="@design-systems/apollo-ui"
    description="Legacy components (Apollo version 1)"
    icon={<History size={24} />}
  />
</div>

<Typography
  level="bodyMedium"
  style={{ marginTop: 16, color: "var(--sb-secondary-text-color)" }}
>
  Tip: Use the toolbar to switch light/dark mode.
</Typography>