import { Tabs as BaseTabs } from "@base-ui-components/react/tabs"
import classNames from "classnames"

import styles from "./tabs.module.css"
import type { TabProps } from "./TabsProps"

export function Tab({
  className,
  variant = "default",
  align = "center",
  ...props
}: TabProps) {
  return (
    <BaseTabs.Tab
      className={classNames(
        "ApolloTabs-tab",
        styles.tabItem,
        {
          [styles.tabsFullContent]: variant === "full",
          [styles.tabAlignLeft]: align === "left",
          [styles.tabAlignRight]: align === "right",
        },
        className
      )}
      {...props}
    />
  )
}
